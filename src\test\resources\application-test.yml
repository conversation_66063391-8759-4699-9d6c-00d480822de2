server:
  port: 0  # 使用随机端口避免冲突

spring:
  application:
    name: branch-agent-test

# 测试环境的Netty服务器配置
xmysfzjjg:
  IP: 127.0.0.1
  PORT: 9702
  WG_PORT: 18888  # 使用不同的端口避免与开发环境冲突
  HTTP_PORT: 19999
  MESSAGE: <?xml version="1.0" encoding="utf-8"?><content><head><statecode>1</statecode><msg>测试成功</msg></head><body><table_account><row><instructionno>TEST001</instructionno><issuccess>1</issuccess></row></table_account></body></content>

# 综合办公系统配置
zhbg:
  zhbgPostUrl: http://127.0.0.1:9091/
  url30001: api/admin/xzp/queryYxjfLsxdye
  url20006: api/admin/xzp/queryJgzhInfo
  reqSysNo: *********
  zhbgPublicKey: test_public_key
  selfPublicKey: test_self_public_key
  privateKey: test_private_key

# WebService客户端配置
webservice:
  client:
    url: http://localhost:8443/wservices/IWebServiceService?wsdl

# 业务转发服务配置
business:
  forwarding:
    enabled: false  # 测试环境禁用转发服务
    port: 18446
    target:
      host: 127.0.0.1
      port: 19090
  target:
    url: http://127.0.0.1:19702
  timeout:
    connect: 30000
    socket: 30000
  # 接口转发地址配置
  endpoints:
    spf20002:
      url: http://127.0.0.1:19702/api/spf/20002

# 日志配置
logging:
  level:
    com.psbc.cpufp.socket: DEBUG
    io.netty: INFO
    root: INFO
