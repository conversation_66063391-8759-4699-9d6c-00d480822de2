# HTTP转发服务前缀配置问题排查与解决方案

## 🚨 问题描述

用户在使用HTTP转发服务时遇到404错误：
```html
<html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Fri Jul 04 09:33:07 CST 2025</div><div>There was an unexpected error (type=Not Found, status=404).</div></body></html>
```

## 🔍 问题根本原因

### 路径映射不匹配问题

1. **HTTP转发服务路径**: `/httpapi/xzp/*`
2. **Spring Boot Controller路径**: `/xzp/*`
3. **转发逻辑缺陷**: 直接将完整路径转发，导致路径不匹配

### 具体问题分析

- HTTP转发服务接收请求: `http://127.0.0.1:8445/httpapi/xzp/test`
- 原转发逻辑直接转发到: `http://127.0.0.1:8443/xmysfzjjg/httpapi/xzp/test`
- Spring Boot实际Controller路径: `http://127.0.0.1:8443/xmysfzjjg/xzp/test`
- **结果**: 404 Not Found

## ✅ 解决方案

### 1. 修复路径转换逻辑

在 `XzpUniversalHandler.java` 中添加路径转换方法：

```java
/**
 * 将HTTP转发服务的路径转换为Spring Boot应用的路径
 * 移除前缀和/httpapi部分，保留/xzp及后续路径
 */
private String convertToSpringBootPath(String requestPath) {
    // 查找/httpapi/xzp的位置
    int httpApiIndex = requestPath.indexOf("/httpapi/xzp");
    if (httpApiIndex != -1) {
        // 提取/xzp及后续路径
        return requestPath.substring(httpApiIndex + "/httpapi".length());
    }
    
    // 兼容性处理
    log.warn("未找到预期的/httpapi/xzp路径模式，原路径: {}", requestPath);
    return requestPath;
}
```

### 2. 支持可配置前缀

#### 配置类增强
```java
// XmysfzjjgProperties.java
private String httpRequestPrefix = "";
```

#### 配置文件
```yaml
# application.yml
xmysfzjjg:
  HTTP_REQUEST_PREFIX: "/api/v1"  # 可选前缀
```

### 3. 路径映射示例

| 请求路径 | 转发路径 | 说明 |
|---------|---------|------|
| `/httpapi/xzp/test` | `/xzp/test` | 无前缀配置 |
| `/api/v1/httpapi/xzp/test` | `/xzp/test` | 配置前缀 `/api/v1` |
| `/branch-agent/httpapi/xzp/queryYxjfLsxdye` | `/xzp/queryYxjfLsxdye` | 配置前缀 `/branch-agent` |

## 🧪 测试验证

### 测试脚本
使用 `test_http_prefix.bat` 进行测试：

```bash
# 无前缀测试
curl -X POST http://127.0.0.1:8445/httpapi/xzp/test \
  -H "Content-Type: application/json" \
  -d '{"test":"data"}'

# 带前缀测试（需要先配置）
curl -X POST http://127.0.0.1:8445/api/v1/httpapi/xzp/test \
  -H "Content-Type: application/json" \
  -d '{"test":"data"}'
```

### 预期响应
```json
{"test":"请求参数json：{\"test\":\"data\"}"}
```

## 📋 Controller路径映射

### XzpController 接口列表

| 接口路径 | 方法 | 功能描述 |
|---------|------|----------|
| `/xzp/queryYxjfLsxdye` | POST | 查询按揭贷款信息 |
| `/xzp/test` | POST | 测试接口 |
| `/xzp/testGet` | GET | GET测试接口 |

### 完整访问路径

- **HTTP转发服务**: `http://127.0.0.1:8445/httpapi/xzp/{接口}`
- **Spring Boot直接访问**: `http://127.0.0.1:8443/xmysfzjjg/xzp/{接口}`

## 🔧 配置建议

### 1. 开发环境
```yaml
xmysfzjjg:
  HTTP_REQUEST_PREFIX: ""  # 无前缀，简化测试
```

### 2. 测试环境
```yaml
xmysfzjjg:
  HTTP_REQUEST_PREFIX: "/test"  # 环境标识
```

### 3. 生产环境
```yaml
xmysfzjjg:
  HTTP_REQUEST_PREFIX: "/api/v1"  # 版本控制
```

## 🚀 优化建议

### 1. 日志监控
- 启用详细的路径转换日志
- 监控转发成功率
- 记录异常请求路径

### 2. 错误处理
- 添加路径验证逻辑
- 提供友好的错误提示
- 支持路径格式自动修正

### 3. 性能优化
- 缓存路径转换结果
- 优化字符串处理逻辑
- 减少不必要的日志输出

## 📝 注意事项

1. **前缀格式**: 自动处理前缀格式（添加/移除斜杠）
2. **兼容性**: 保持向下兼容，支持无前缀配置
3. **测试**: 修改配置后需要重启服务
4. **监控**: 关注转发日志，确保路径映射正确

## 🔗 相关文档

- [HTTP 8445端口使用指南](HTTP_8445_PORT_GUIDE.md)
- [架构优化说明](ARCHITECTURE_OPTIMIZATION.md)
- [项目README](README.md)