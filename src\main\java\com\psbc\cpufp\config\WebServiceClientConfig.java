package com.psbc.cpufp.config;

import com.psbc.cpufp.service.ClientIWebService;
import org.apache.cxf.configuration.jsse.TLSClientParameters;
import org.apache.cxf.frontend.ClientProxy;
import org.apache.cxf.jaxws.JaxWsProxyFactoryBean;
import org.apache.cxf.transport.http.HTTPConduit;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.cert.X509Certificate;

@Configuration
public class WebServiceClientConfig {

    @Value("${webservice.client.url}")
    private String serviceUrl;

    @Bean
    public ClientIWebService webServiceClient() {
        JaxWsProxyFactoryBean factoryBean = new JaxWsProxyFactoryBean();
        factoryBean.setServiceClass(ClientIWebService.class);
        factoryBean.setAddress(serviceUrl);

        // 创建客户端
        ClientIWebService client = (ClientIWebService) factoryBean.create();
        
        // 配置 SSL（如果需要）
        configureSSL(client);
        
        return client;
    }

    private void configureSSL(ClientIWebService client) {
        org.apache.cxf.endpoint.Client cl = ClientProxy.getClient(client);
        HTTPConduit http = (HTTPConduit) cl.getConduit();

        TLSClientParameters tlsParams = new TLSClientParameters();
        // 设置SSL协议版本
        tlsParams.setSecureSocketProtocol("TLS");
        
        // 配置信任所有证书（仅用于开发测试）
        tlsParams.setTrustManagers(new TrustManager[] { new X509TrustManager() {
            public void checkClientTrusted(X509Certificate[] chain, String authType) {}
            public void checkServerTrusted(X509Certificate[] chain, String authType) {}
            public X509Certificate[] getAcceptedIssuers() { return null; }
        }});
        tlsParams.setDisableCNCheck(true);
        
        http.setTlsClientParameters(tlsParams);
    }
}