# HTTP 8445端口使用指南

## 📋 概述

本项目新增了HTTP 8445端口服务，用于接收HTTP请求并转发到Spring Boot应用的对应接口。该服务通过 `HttpForwardingService` 组件实现，与主应用的8443端口和Netty Socket的8888端口并行运行。

## 端口配置

- **Spring Boot应用端口**: 8443 (server.port)
- **Netty Socket端口**: 8888 (WG_PORT)
- **HTTP服务端口**: 8445 (HTTP_PORT) ✨ **新增**
- **WebService端口**: 根据环境配置

## 可用接口

通过8445端口可以访问以下接口：

### 1. 查询有效缴费历史信息
```bash
# POST请求
curl -X POST http://127.0.0.1:8445/xzp/queryYxjfLsxdye \
  -H "Content-Type: application/json" \
  -d '{"your":"json","data":"here"}'
```

### 2. 测试接口 (POST)
```bash
# POST请求
curl -X POST http://127.0.0.1:8445/xzp/test \
  -H "Content-Type: application/json" \
  -d '{"test":"data"}'
```

### 3. 测试接口 (GET)
```bash
# GET请求
curl -X GET http://127.0.0.1:8445/xzp/testGet
```

## 🔧 实现原理

### 核心组件
- **HttpForwardingService**: Spring组件，通过 `@PostConstruct` 自动启动HTTP转发服务
- **XzpUniversalHandler**: 通用HTTP请求处理器，处理所有 `/xzp` 路径的请求
- **MyHttpHandler**: 特定路径的处理器（保留兼容性）

### 工作流程
1. Spring容器启动时，`HttpForwardingService` 通过 `@PostConstruct` 自动初始化
2. HTTP服务器监听8445端口，接收外部请求
3. 请求通过 `XzpUniversalHandler` 转发到Spring Boot应用
4. 返回Spring Boot应用的响应结果
5. 应用关闭时，通过 `@PreDestroy` 自动停止HTTP服务

### 配置层面
- 在 `application.yml` 中配置 `xmysfzjjg.HTTP_PORT: 8445`
- 支持local、dev、master三个环境

### 请求流程
```
客户端请求 → HTTP服务(8445端口) → XzpUniversalHandler → Spring Boot应用(8443端口) → 返回响应
```

## 优势

- ✅ **端口独立性**: 8445端口独立运行，不影响现有服务
- ✅ **无缝转发**: 自动转发请求到Spring Boot应用
- ✅ **统一接口**: 保持与原有Controller相同的接口路径
- ✅ **易于维护**: 利用现有架构，代码复用性高
- ✅ **灵活配置**: 支持多环境配置

## 注意事项

1. 确保8445端口未被其他应用占用
2. Spring Boot应用必须正常运行在8443端口
3. 请求路径保持与原Controller一致（/xzp/xxx）
4. 支持POST和GET请求方法

## 日志监控

启动时会看到以下日志信息：
```
启动httpServer成功,端口：8445
HTTP服务已启动，可通过以下地址访问:
  - http://127.0.0.1:8445/xzp/queryYxjfLsxdye (POST)
  - http://127.0.0.1:8445/xzp/test (POST)
  - http://127.0.0.1:8445/xzp/testGet (GET)
```

## 🚀 架构优化说明

### 优化内容
本项目已进行架构优化，移除了 `SocketListener`，改用更清晰的组件分离架构：

1. **HttpForwardingService**: 专门负责HTTP转发服务
2. **PayServerSocket**: 专门负责Netty Socket服务
3. **Spring Boot Web**: 主Web服务

### 优化效果
- ✅ 职责分离更清晰
- ✅ 避免重复启动问题
- ✅ 更好的Spring生命周期管理
- ✅ 更容易维护和扩展

## 🧪 测试脚本

项目提供了 `test_8445_port.bat` 测试脚本，用于验证8445端口服务：

```batch
@echo off
echo 测试8445端口HTTP服务...
echo.

echo 1. 检查8445端口监听状态:
netstat -an | findstr :8445
echo.

echo 2. 测试GET请求 - /xzp/testGet:
curl -X GET "http://127.0.0.1:8445/xzp/testGet"
echo.
echo.

echo 3. 测试POST请求 - /xzp/test:
curl -X POST "http://127.0.0.1:8445/xzp/test" -H "Content-Type: application/json" -d "{\"message\":\"test\"}"
echo.
echo.

echo 4. 测试POST请求 - /xzp/queryYxjfLsxdye:
curl -X POST "http://127.0.0.1:8445/xzp/queryYxjfLsxdye" -H "Content-Type: application/json" -d "{\"test\":\"data\"}"
echo.
echo.

echo 测试完成！
pause
```

## 🔍 故障排除

### 1. 检查启动日志
应用启动时应该看到以下日志：
```
HTTP转发服务启动成功，端口：8445
HTTP服务已启动，可通过以下地址访问:
  - http://127.0.0.1:8445/xzp/queryYxjfLsxdye (POST)
  - http://127.0.0.1:8445/xzp/test (POST)
  - http://127.0.0.1:8445/xzp/testGet (GET)
```

### 2. 检查端口占用
```bash
netstat -an | findstr :8445
```

### 3. 确认HttpForwardingService启动
检查启动日志中是否有 `HttpForwardingService` 相关的启动信息。

### 4. 验证配置
确认 `application.yml` 中配置了：
```yaml
xmysfzjjg:
  HTTP_PORT: 8445
```

### 5. 检查组件扫描
确认 `HttpForwardingService` 在Spring的组件扫描范围内（`com.psbc.cpufp` 包下）。

### 6. 其他检查项
- Spring Boot应用(8443端口)是否正常运行
- 防火墙设置是否允许8445端口访问